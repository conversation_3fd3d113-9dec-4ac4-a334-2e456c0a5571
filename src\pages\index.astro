---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getEntry, getCollection } from 'astro:content';
import { siteConfig } from '../config/site';
import { contentConfig, getExperienceHighlights } from '../config/content';
import { dynamicContent } from '../config/content-generator';

const homepageContent = await getEntry('homepage', 'main');
const { hero, about, sections } = homepageContent.data;

// Get latest portfolio projects for preview
const portfolioProjects = await getCollection('portfolio', ({ slug }) => 
  !slug.startsWith('fallback/')
);

let featuredProjects;

if (portfolioProjects.length === 0) {
  // Load fallback projects when no real projects exist
  const fallbackProjects = await getCollection('portfolio', ({ slug }) => 
    slug.startsWith('fallback/')
  );
  featuredProjects = fallbackProjects
    .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
    .slice(0, 3);
} else {
  featuredProjects = portfolioProjects
    .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
    .slice(0, 3);
}

const experienceHighlights = getExperienceHighlights();
---

<Layout title={siteConfig.title}>
  <!-- Hero Section -->
  <section class="hero relative pt-19 pb-22 lg:pt-22 lg:pb-28 overflow-hidden" role="banner">
    <!-- Professional gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/40 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/40"></div>

    <!-- Sophisticated background elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- Subtle organic shapes for depth -->
      <div class="absolute top-1/4 left-1/4 w-80 h-80 bg-gradient-to-br from-primary-300/8 to-accent-300/6 rounded-full blur-3xl animate-float"></div>
      <div class="absolute bottom-1/3 right-1/4 w-96 h-96 bg-gradient-to-br from-accent-300/6 to-primary-300/8 rounded-full blur-3xl animate-float" style="animation-delay: 2s; animation-duration: 5s;"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-secondary-200/6 to-primary-200/4 rounded-full blur-2xl animate-bounce-subtle"></div>

      <!-- Refined texture overlay -->
      <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;80&quot; height=&quot;80&quot; viewBox=&quot;0 0 80 80&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23a19e92&quot; fill-opacity=&quot;0.03&quot;%3E%3Ccircle cx=&quot;40&quot; cy=&quot;40&quot; r=&quot;0.8&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="container-custom relative z-10 py-20">
      <div class="text-center">

        <!-- Main heading with modern typography -->
        <div class="mb-8 animate-fade-in">
          <h1 class="heading-xl mb-6">
            <span set:html={hero.headline}></span>
          </h1>

          <h2 class="text-xl sm:text-2xl lg:text-3xl text-text-light-secondary dark:text-text-dark-secondary mb-6 font-medium max-w-4xl mx-auto leading-relaxed">
            {hero.subheadline}
          </h2>

          <p class="text-lg sm:text-xl max-w-3xl mx-auto text-text-light-muted dark:text-text-dark-muted leading-relaxed">
            {hero.description}
          </p>
        </div>

        <!-- Modern feature highlights with bento-style layout -->
        <div class="features grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-5xl mx-auto animate-slide-up" style="animation-delay: 0.2s;">
          {hero.highlights.map((highlight, index) => (
            <div class="feature-item group bg-white/60 dark:bg-secondary-900/40 backdrop-blur-sm border border-secondary-200/30 dark:border-secondary-700/30 rounded-2xl p-6 text-center hover:bg-white/80 dark:hover:bg-secondary-800/60 hover:border-primary-300/40 dark:hover:border-primary-600/40 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg" style={`animation-delay: ${0.3 + index * 0.1}s;`}>
              <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">{highlight.icon}</div>
              <p class="text-sm font-medium text-text-light-secondary dark:text-text-dark-secondary group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">{highlight.label}</p>
            </div>
          ))}
        </div>

        <!-- Enhanced CTA buttons with modern design -->
        <div class="cta-buttons flex justify-center gap-4 flex-col sm:flex-row animate-slide-up" style="animation-delay: 0.6s;">
          <a href={hero.primaryCTA.url} class="btn-primary group text-lg px-8 py-4" aria-label="View my portfolio projects">
            {hero.primaryCTA.text}
            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a href={hero.secondaryCTA.url} class="btn-secondary group text-lg px-8 py-4" aria-label="Get in touch with me">
            {hero.secondaryCTA.text}
            <svg class="w-5 h-5 ml-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="section bg-background-light-secondary dark:bg-background-dark-secondary relative overflow-hidden" role="main">
    <!-- Sophisticated background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-10 right-10 w-80 h-80 bg-gradient-to-br from-accent-300/4 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-tl from-primary-300/4 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="text-center mb-16">
        <h2 class="heading-lg text-text-light dark:text-text-dark mb-4 relative">
          {sections?.aboutMe?.heading || "About Me"}
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-600 rounded"></span>
        </h2>
        {sections?.aboutMe?.subheading && (
          <p class="text-lg text-text-light-muted dark:text-text-dark-muted max-w-2xl mx-auto">
            {sections.aboutMe.subheading}
          </p>
        )}
      </div>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
        <div class="about-content">
          <div class="about-text space-y-6 mb-12">
            <p class="text-lg font-medium text-text-light-secondary dark:text-text-dark-secondary leading-relaxed">
              {about.openingLine}
            </p>
            {about.mainContent.map((paragraph: string) => (
              <p class="text-base leading-relaxed text-text-light-muted dark:text-text-dark-muted">
                {paragraph}
              </p>
            ))}
          </div>

          <div class="experience-highlights">
            <h3 class="text-xl font-bold text-text-light dark:text-text-dark mb-5 font-heading">Experience Highlights</h3>
            <ul class="space-y-3">
              {experienceHighlights.map(highlight => (
                <li class="flex items-center">
                  <span class="text-primary-600 dark:text-primary-400 mr-3 font-bold text-lg">✓</span>
                  <span class="text-text-light-muted dark:text-text-dark-muted">{highlight.text}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
        
        <!-- Technical Skills -->
        <div class="skills-section">
          <div class="bg-gradient-to-br from-white/95 via-white/90 to-secondary-50/60 dark:from-secondary-900/80 dark:via-secondary-800/80 dark:to-secondary-700/60 p-8 lg:p-10 rounded-3xl shadow-xl border border-secondary-200/40 dark:border-secondary-600/40 backdrop-blur-sm">
            <h3 class="text-2xl font-bold text-text-light dark:text-text-dark mb-8 text-center font-heading flex items-center justify-center gap-3">
              <span class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-600 rounded-lg flex items-center justify-center text-white text-sm">⚡</span>
              Professional Skills
            </h3>

            <div class="grid grid-cols-1 gap-6">
              <!-- Primary Skills -->
              <div class="skill-category group bg-white/90 dark:bg-secondary-800/90 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-primary-300/20 dark:border-primary-600/30">
                <h4 class="text-lg font-semibold text-primary-700 dark:text-primary-300 mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white text-xs">🔧</span>
                  {contentConfig.skillCategories.primary}
                </h4>
                <div class="flex flex-wrap gap-2">
                  {contentConfig.skills.primary.map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-primary-400/15 to-primary-300/10 dark:from-primary-500/25 dark:to-primary-400/20 text-primary-700 dark:text-primary-200 text-sm font-medium rounded-full border border-primary-400/25 dark:border-primary-500/30 hover:bg-gradient-to-r hover:from-primary-400/25 hover:to-primary-300/20 dark:hover:from-primary-500/35 dark:hover:to-primary-400/30 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              <!-- Secondary Skills -->
              <div class="skill-category group bg-white/90 dark:bg-secondary-800/90 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-secondary-300/25 dark:border-secondary-600/35">
                <h4 class="text-lg font-semibold text-secondary-700 dark:text-secondary-200 mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-lg flex items-center justify-center text-white text-xs">🏗️</span>
                  {contentConfig.skillCategories.secondary}
                </h4>
                <div class="flex flex-wrap gap-2">
                  {contentConfig.skills.secondary.map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-secondary-400/15 to-secondary-300/10 dark:from-secondary-500/25 dark:to-secondary-400/20 text-secondary-700 dark:text-secondary-200 text-sm font-medium rounded-full border border-secondary-400/25 dark:border-secondary-500/30 hover:bg-gradient-to-r hover:from-secondary-400/25 hover:to-secondary-300/20 dark:hover:from-secondary-500/35 dark:hover:to-secondary-400/30 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              <!-- Tools & Platforms -->
              <div class="skill-category group bg-white/90 dark:bg-secondary-800/90 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-accent-300/25 dark:border-accent-600/35">
                <h4 class="text-lg font-semibold text-accent-700 dark:text-accent-300 mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-accent-500 to-accent-600 rounded-lg flex items-center justify-center text-white text-xs">⚙️</span>
                  {contentConfig.skillCategories.tools}
                </h4>
                <div class="flex flex-wrap gap-2">
                  {contentConfig.skills.tools.map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-accent-400/15 to-accent-300/10 dark:from-accent-500/25 dark:to-accent-400/20 text-accent-700 dark:text-accent-200 text-sm font-medium rounded-full border border-accent-400/25 dark:border-accent-500/30 hover:bg-gradient-to-r hover:from-accent-400/25 hover:to-accent-300/20 dark:hover:from-accent-500/35 dark:hover:to-accent-400/30 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            
            <!-- Call to action -->
            <div class="mt-8 text-center">
              <a href="/resume" class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 hover:-translate-y-1 text-sm hover:from-primary-600 hover:to-primary-700">
                {siteConfig.labels.buttons.viewFullResume}
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="section bg-background-light dark:bg-background-dark relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <!-- Sophisticated background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-10 w-80 h-80 bg-gradient-to-br from-primary-300/4 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-tl from-accent-300/4 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="text-center mb-16">
        <h2 id="portfolio-title" class="heading-lg text-text-light dark:text-text-dark mb-4 relative">
          {sections?.portfolio?.heading || "Featured Projects"}
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-600 rounded"></span>
        </h2>
        <p class="text-lg text-text-light-muted dark:text-text-dark-muted max-w-2xl mx-auto">
          {sections?.portfolio?.subheading || "A showcase of scalable systems and innovative solutions I've built"}
        </p>
      </div>
      
      <!-- Modern Portfolio Grid -->
      <div class="portfolio-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12" role="list">
        {featuredProjects.map((project, index) => (
          <div
            class="portfolio-item"
            style={`animation-delay: ${index * 0.1}s;`}
            role="listitem"
          >
            <a href={`/portfolio/${project.slug}`} class="block h-full">
              <ProjectCard
                title={project.data.title}
                description={project.data.description || project.data.problem}
                tags={project.data.tags}
                slug={project.slug}
                image={project.data.image}
                technologies={project.data.technologies}
                github={project.data.github}
                live={project.data.live}
                publishDate={project.data.publishDate}
                featured={project.data.featured}
              />
            </a>
          </div>
        ))}
      </div>

      <!-- Modern Portfolio CTA -->
      <div class="text-center">
        <a href="/portfolio" class="btn-primary group text-lg px-8 py-4">
          {siteConfig.labels.buttons.viewAllProjects}
          <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
  
  <!-- Call to Action Section -->
  <section class="cta-section py-24 relative overflow-hidden">
    <!-- Enhanced gradient background with sophisticated warm tones -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-500 via-primary-600 to-accent-600 dark:from-primary-700 dark:via-primary-800 dark:to-accent-700"></div>

    <!-- Sophisticated overlay with depth -->
    <div class="absolute inset-0 bg-gradient-to-t from-secondary-900/20 via-transparent to-secondary-900/10 dark:from-secondary-950/30 dark:via-transparent dark:to-secondary-950/15"></div>

    <!-- Subtle pattern overlay for texture -->
    <div class="absolute inset-0 opacity-10 dark:opacity-5" style="background-image: radial-gradient(circle at 2px 2px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 40px 40px;"></div>

    <!-- Content container -->
    <div class="container mx-auto px-5 max-w-4xl relative z-10 text-center">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6 font-heading drop-shadow-sm">
        {sections?.cta?.heading || "Ready to Build Something Amazing?"}
      </h2>
      <p class="text-xl text-white/95 mb-8 max-w-2xl mx-auto leading-relaxed drop-shadow-sm">
        {sections?.cta?.subheading || "Let's discuss your next project and how I can help you build scalable, high-performance solutions."}
      </p>
      <div class="flex justify-center gap-6 flex-col sm:flex-row">
        <!-- Primary CTA Button with enhanced styling -->
        <a href="/contact" class="group inline-flex items-center justify-center gap-3 px-8 py-4 bg-white/95 backdrop-blur-sm text-primary-700 rounded-2xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:scale-105 text-lg hover:bg-white border border-white/20">
          {siteConfig.labels.buttons.startConversation}
          <svg class="w-5 h-5 group-hover:scale-110 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </a>

        <!-- Secondary CTA Button with glassmorphism -->
        <a href="/resume" class="group inline-flex items-center justify-center gap-3 px-8 py-4 border-2 border-white/80 text-white rounded-2xl font-semibold hover:bg-white/10 backdrop-blur-sm transition-all duration-500 hover:-translate-y-2 hover:scale-105 text-lg hover:border-white hover:shadow-xl">
          {siteConfig.labels.buttons.downloadResume}
          <svg class="w-5 h-5 group-hover:translate-y-1 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</Layout> 